import MySQLdb

# Connexion à la base de données
try:
    conn = MySQLdb.connect(host="localhost", user="root", passwd="", db="gestion_vehicules")
    cursor = conn.cursor()
    
    # Vérifier si la colonne existe déjà
    cursor.execute("SHOW COLUMNS FROM vehicule LIKE 'unite'")
    result = cursor.fetchone()
    
    if not result:
        # Ajouter la colonne manquante
        cursor.execute("ALTER TABLE vehicule ADD COLUMN unite VARCHAR(20)")
        conn.commit()
        print("Colonne 'unite' ajoutée avec succès à la table 'vehicule'")
    else:
        print("La colonne 'unite' existe déjà dans la table 'vehicule'")
    
    # Vérifier la structure de la table
    cursor.execute("DESCRIBE vehicule")
    columns = cursor.fetchall()
    print("\nStructure de la table 'vehicule':")
    for column in columns:
        print(column)
    
    conn.close()
    
except MySQLdb.Error as e:
    print(f"Erreur MySQL: {e}")
