import mysql.connector
from mysql.connector import Error

def restore_database():
    try:
        # Connexion à MySQL sans spécifier de base de données
        connection = mysql.connector.connect(
            host="localhost",
            user="root",
            password=""
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Supprimer la base de données si elle existe
            cursor.execute("DROP DATABASE IF EXISTS gestion_vehicules")
            print("Base de données supprimée avec succès (si elle existait)")
            
            # Créer une nouvelle base de données
            cursor.execute("CREATE DATABASE gestion_vehicules")
            print("Base de données créée avec succès")
            
            # Utiliser la nouvelle base de données
            cursor.execute("USE gestion_vehicules")
            
            # Créer les tables selon le fichier SQL
            cursor.execute("""
            CREATE TABLE types_vehicules (
                id INT AUTO_INCREMENT PRIMARY KEY,
                type VARCHAR(255) NOT NULL
            )
            """)
            print("Table types_vehicules créée avec succès")
            
            cursor.execute("""
            CREATE TABLE marques (
                id INT AUTO_INCREMENT PRIMARY KEY,
                marque VARCHAR(255) NOT NULL,
                type_vehicule_id INT,
                FOREIGN KEY (type_vehicule_id) REFERENCES types_vehicules(id)
            )
            """)
            print("Table marques créée avec succès")
            
            cursor.execute("""
            CREATE TABLE types_pannes (
                id INT AUTO_INCREMENT PRIMARY KEY,
                panne VARCHAR(255) NOT NULL
            )
            """)
            print("Table types_pannes créée avec succès")
            
            cursor.execute("""
            CREATE TABLE vehicules (
                id INT AUTO_INCREMENT PRIMARY KEY,
                type_vehicule VARCHAR(255) NOT NULL,
                marque VARCHAR(255) NOT NULL,
                modele VARCHAR(255) NOT NULL,
                type_panne VARCHAR(255),
                date_panne DATE,
                description TEXT,
                unite_vehicule VARCHAR(255) NOT NULL
            )
            """)
            print("Table vehicules créée avec succès")
            
            # Ajouter la table vehicule pour la compatibilité avec l'application actuelle
            cursor.execute("""
            CREATE TABLE vehicule (
                id INT AUTO_INCREMENT PRIMARY KEY,
                type_vehicule VARCHAR(20) NOT NULL,
                marque VARCHAR(50) NOT NULL,
                modele VARCHAR(50) NOT NULL,
                type_panne VARCHAR(100) NOT NULL,
                date_panne DATE NOT NULL,
                description TEXT,
                statut VARCHAR(20) DEFAULT 'En panne',
                unite VARCHAR(20)
            )
            """)
            print("Table vehicule créée avec succès (pour compatibilité)")
            
            # Ajouter quelques données de test
            insert_query = """
            INSERT INTO vehicule (type_vehicule, marque, modele, type_panne, date_panne, description, statut, unite)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            test_data = [
                ('VL', 'Toyota', 'ABC123', 'Batterie déchargée', '2023-05-15', 'Véhicule ne démarre pas', 'En panne', '13 GAR'),
                ('PL', 'Renault', 'XYZ789', 'Embrayage défectueux', '2023-04-20', 'Difficulté à passer les vitesses', 'En réparation', '14 GAR'),
                ('Engin chenillé', 'M109 155mm', 'DEF456', 'Problème de chenille', '2023-03-10', 'Chenille droite endommagée', 'Réparé', '18 GAR'),
                ('SA', 'CESAR', 'GHI789', 'Système hydraulique défaillant', '2023-06-01', 'Fuite hydraulique', 'Indisponible', '20 GAR')
            ]
            
            cursor.executemany(insert_query, test_data)
            connection.commit()
            print(f"{cursor.rowcount} véhicules ajoutés avec succès")
            
            # Ajouter des données dans les tables de référence
            # Types de véhicules
            cursor.execute("INSERT INTO types_vehicules (type) VALUES ('VL'), ('PL'), ('Engin chenillé'), ('SA')")
            
            # Marques
            cursor.execute("""
            INSERT INTO marques (marque, type_vehicule_id) VALUES 
            ('Toyota', 1), ('Atlas 3', 1), ('Atlas 4', 1), ('Atlas 5', 1), ('VLRA', 1), ('Hummer', 1), ('Nissan', 1),
            ('JBD', 2), ('Kaiser', 2), ('Renault', 2),
            ('M109 155mm', 3), ('M110 203mm', 3), ('Vulcan', 3),
            ('PULS', 4), ('HIMARS', 4), ('CESAR', 4)
            """)
            
            # Types de pannes
            cursor.execute("""
            INSERT INTO types_pannes (panne) VALUES 
            ('Batterie déchargée'), ('Alternateur défectueux'), ('Embrayage défectueux'), 
            ('Problème de transmission'), ('Fuite d''huile moteur'), ('Système hydraulique défaillant'),
            ('Problème de chenille'), ('Problème de démarrage'), ('Surchauffe moteur')
            """)
            
            connection.commit()
            print("Données de référence ajoutées avec succès")
            
    except Error as e:
        print(f"Erreur lors de la connexion à MySQL: {e}")
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            print("Connexion MySQL fermée")

if __name__ == "__main__":
    restore_database()
