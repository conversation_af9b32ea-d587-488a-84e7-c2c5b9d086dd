import mysql.connector
from datetime import datetime

# Connexion à la base de données
conn = mysql.connector.connect(
    host="localhost",
    user="root",
    password="",
    database="gestion_vehicules"
)
cursor = conn.cursor()

# Vérifier si la table existe déjà
cursor.execute("SHOW TABLES LIKE 'vehicule_historique'")
table_exists = cursor.fetchone()

if not table_exists:
    # Créer la table vehicule_historique
    print("Création de la table vehicule_historique...")
    cursor.execute("""
    CREATE TABLE vehicule_historique (
        id INT AUTO_INCREMENT PRIMARY KEY,
        vehicule_id INT NOT NULL,
        date_changement DATETIME NOT NULL,
        statut VARCHAR(20) NOT NULL,
        description TEXT,
        FOREIGN KEY (vehicule_id) REFERENCES vehicule(id)
    )
    """)
    
    # Récupérer tous les véhicules
    cursor.execute("SELECT id, statut FROM vehicule")
    vehicules = cursor.fetchall()
    
    # Créer un historique initial pour chaque véhicule
    print(f"Création de l'historique initial pour {len(vehicules)} véhicules...")
    for vehicule_id, statut in vehicules:
        cursor.execute("""
        INSERT INTO vehicule_historique (vehicule_id, date_changement, statut, description)
        VALUES (%s, %s, %s, %s)
        """, (vehicule_id, datetime.now(), statut, "État initial (reconstitué lors de la création de la table d'historique)"))
    
    conn.commit()
    print(f"Historique créé pour {len(vehicules)} véhicules.")
else:
    print("La table vehicule_historique existe déjà.")

# Fermer la connexion
cursor.close()
conn.close()
print("Opération terminée avec succès!")
