from flask import Flask
from db import db, init_app
from sqlalchemy import text

app = Flask(__name__)
init_app(app)

with app.app_context():
    # Ajouter les colonnes manquantes
    db.session.execute(text('ALTER TABLE entretien ADD COLUMN prochain_entretien DATETIME NULL'))
    db.session.execute(text('ALTER TABLE entretien ADD COLUMN statut VARCHAR(20) NOT NULL DEFAULT "Planifié"'))
    db.session.commit()
    print("Colonnes ajoutées avec succès !") 