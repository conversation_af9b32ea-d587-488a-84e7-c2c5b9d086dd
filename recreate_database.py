import mysql.connector
from mysql.connector import <PERSON>rro<PERSON>

def recreate_database():
    try:
        # Connexion à MySQL sans spécifier de base de données
        connection = mysql.connector.connect(
            host="localhost",
            user="root",
            password=""
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            
            # Supprimer la base de données si elle existe
            cursor.execute("DROP DATABASE IF EXISTS gestion_vehicules")
            print("Base de données supprimée avec succès (si elle existait)")
            
            # Créer une nouvelle base de données
            cursor.execute("CREATE DATABASE gestion_vehicules")
            print("Base de données créée avec succès")
            
            # Utiliser la nouvelle base de données
            cursor.execute("USE gestion_vehicules")
            
            # Créer la table vehicule
            cursor.execute("""
            CREATE TABLE vehicule (
                id INT AUTO_INCREMENT PRIMARY KEY,
                type_vehicule VARCHAR(20) NOT NULL,
                marque VARCHAR(50) NOT NULL,
                modele VARCHAR(50) NOT NULL,
                type_panne VARCHAR(100) NOT NULL,
                date_panne DATE NOT NULL,
                description TEXT,
                statut VARCHAR(20) DEFAULT 'En panne',
                unite VARCHAR(20)
            )
            """)
            print("Table vehicule créée avec succès")
            
            # Ajouter quelques données de test
            insert_query = """
            INSERT INTO vehicule (type_vehicule, marque, modele, type_panne, date_panne, description, statut, unite)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            test_data = [
                ('VL', 'Toyota', 'ABC123', 'Batterie déchargée', '2023-05-15', 'Véhicule ne démarre pas', 'En panne', '13 GAR'),
                ('PL', 'Renault', 'XYZ789', 'Embrayage défectueux', '2023-04-20', 'Difficulté à passer les vitesses', 'En réparation', '14 GAR'),
                ('Engin chenillé', 'M109 155mm', 'DEF456', 'Problème de chenille', '2023-03-10', 'Chenille droite endommagée', 'Réparé', '18 GAR'),
                ('SA', 'CESAR', 'GHI789', 'Système hydraulique défaillant', '2023-06-01', 'Fuite hydraulique', 'Indisponible', '20 GAR')
            ]
            
            cursor.executemany(insert_query, test_data)
            connection.commit()
            print(f"{cursor.rowcount} véhicules ajoutés avec succès")
            
    except Error as e:
        print(f"Erreur lors de la connexion à MySQL: {e}")
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
            print("Connexion MySQL fermée")

if __name__ == "__main__":
    recreate_database()
