{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col">
            <h2>Gestion des Vidanges</h2>
        </div>
        <div class="col-auto">
            <button class="btn btn-primary" id="btnNouvelleVidange">
                <i class="fas fa-plus"></i> Nouvelle Vidange
            </button>
        </div>
    </div>

    <!-- Barre de recherche -->
    <div class="card mb-4">
        <div class="card-body bg-light py-3">
            <form action="{{ url_for('entretiens') }}" method="get" class="d-flex">
                <div class="input-group">
                    <input type="text" name="search_matricule" class="form-control" placeholder="Rechercher les entretiens par matricule..." value="{{ search_matricule }}" aria-label="Rechercher par matricule">
                    <button class="btn btn-outline-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                    {% if search_matricule %}
                    <a href="{{ url_for('entretiens') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Effacer
                    </a>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>

    <!-- Section de vérification du véhicule -->
    <div id="verificationSection" class="card mb-4" style="display: none;">
        <div class="card-header">
            <h5 class="mb-0">Vérification du Véhicule</h5>
        </div>
        <div class="card-body">
            <form id="verificationForm">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="matricule">Matricule</label>
                            <input type="text" class="form-control" id="matricule" name="matricule" required>
                        </div>
                    </div>
                </div>
                <div id="vehiculeInfoDisplay" class="alert alert-info mt-3" style="display: none;">
                    <h6>Informations du Véhicule</h6>
                    <p id="vehiculeDetailsDisplay"></p>
                </div>
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary" id="btnVerifierVehicule">Vérifier</button>
                    <button type="button" class="btn btn-success" id="btnConfirmerVehicule" style="display: none;">Confirmer et Ajouter Vidange</button>
                    <button type="button" class="btn btn-secondary" id="btnAnnulerVerification">Annuler</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Section d'ajout de vidange -->
    <div id="ajoutVidangeSection" class="card mb-4" style="display: none;">
        <div class="card-header">
            <h5 class="mb-0">Nouvelle Vidange pour Véhicule <span id="ajoutVidangeMatricule"></span></h5>
        </div>
        <div class="card-body">
            <form id="ajoutVidangeForm" action="{{ url_for('ajouter_entretien') }}" method="POST">
                <input type="hidden" id="vehicule_id_ajout" name="vehicule_id">
                <input type="hidden" name="type_entretien" value="Vidange">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="date_entretien">Date de Vidange</label>
                            <input type="date" class="form-control" id="date_entretien" name="date_entretien" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="kilometrage">Kilométrage Actuel</label>
                            <input type="number" class="form-control" id="kilometrage" name="kilometrage" required>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="kilometrage_prochain">Prochain Kilométrage</label>
                            <input type="number" class="form-control" id="kilometrage_prochain" name="kilometrage_prochain" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Pièces Remplacées</label>
                            <div id="piecesContainer">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" value="Filtre à huile" id="piece1">
                                    <label class="form-check-label" for="piece1">Filtre à huile</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" value="Filtre à air" id="piece2">
                                    <label class="form-check-label" for="piece2">Filtre à air</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" value="Filtre à carburant" id="piece3">
                                    <label class="form-check-label" for="piece3">Filtre à carburant</label>
                                </div>
                            </div>
                            <div class="mt-2">
                                <input type="text" class="form-control" id="autrePiece" placeholder="Autre pièce...">
                                <button type="button" class="btn btn-sm btn-secondary mt-2" id="btnAjouterPiece">Ajouter</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="form-group">
                            <label for="description">Description/Commentaires (optionnel)</label>
                            <textarea class="form-control" id="description" name="description" rows="3" placeholder="Ajoutez des commentaires sur cette vidange..."></textarea>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                    <button type="button" class="btn btn-secondary" id="btnAnnulerAjout">Annuler</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Tableau des vidanges -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped" id="vidangesTable">
                    <thead>
                        <tr>
                            <th>Matricule</th>
                            <th>Unité</th>
                            <th>Date</th>
                            <th>Kilométrage</th>
                            <th>Prochain Kilométrage</th>
                            <th>Pièces Remplacées</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for entretien in entretiens %}
                        <tr>
                            <td>{{ entretien.vehicule.matricule }}</td>
                            <td>{{ entretien.vehicule.unite }}</td>
                            <td>{{ entretien.date_entretien.strftime('%d/%m/%Y') }}</td>
                            <td>{{ entretien.kilometrage }}</td>
                            <td>{{ entretien.kilometrage_prochain }}</td>
                            <td>{{ entretien.pieces_remplacees or 'Aucune' }}</td>
                            <td>
                                <button class="btn btn-sm btn-info btn-update-km" data-vehicule-id="{{ entretien.vehicule.id }}">
                                    <i class="fas fa-edit"></i> Mettre à jour km
                                </button>
                                <button class="btn btn-sm btn-danger btn-delete-entretien" data-entretien-id="{{ entretien.id }}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Section de mise à jour du kilométrage -->
<div id="updateKilometrageSection" class="card mb-4" style="display: none;">
    <div class="card-header text-white" style="background-color: #4b5320;">
        <h5 class="mb-0">Mise à jour du kilométrage</h5>
    </div>
    <div class="card-body">
        <form id="formMiseAJourKilometrage" class="row g-3 align-items-end">
            <input type="hidden" id="vehicule_id_update" name="vehicule_id">
            <div class="col-md-6">
                <label for="nouveau_kilometrage" class="form-label">Nouveau kilométrage</label>
                <div class="input-group">
                    <input type="number" 
                           class="form-control form-control-lg" 
                           id="nouveau_kilometrage" 
                           name="kilometrage" 
                           required 
                           min="0" 
                           step="1"
                           placeholder="Entrez le kilométrage actuel">
                    <span class="input-group-text">km</span>
                </div>
                <div class="form-text">Le kilométrage doit être supérieur au précédent</div>
                <div id="kilometrageFeedback" class="invalid-feedback">
                    Veuillez entrer un kilométrage valide
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-grid gap-2 d-md-flex">
                    <button type="submit" class="btn btn-primary" id="btnSaveKilometrage">
                        <i class="fas fa-save me-1"></i> Enregistrer
                    </button>
                    <button type="button" class="btn btn-outline-secondary" id="btnCancelKilometrage">
                        <i class="fas fa-times me-1"></i> Annuler
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Toast pour les notifications -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div id="toastVidanges" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto">Vidanges à venir</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toastVidangesBody">
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/entretiens.js') }}"></script>
{% endblock %}
{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded and parsed. Initializing entretien page scripts.');

    // Vérification de l'existence des éléments
    const btnNouvelleVidange = document.getElementById('btnNouvelleVidange');
    const verificationSection = document.getElementById('verificationSection');
    const ajoutVidangeSection = document.getElementById('ajoutVidangeSection');
    const vidangesTableBody = document.querySelector('#vidangesTable tbody');
    const btnConfirmerVehicule = document.getElementById('btnConfirmerVehicule');
    const btnAnnulerVerification = document.getElementById('btnAnnulerVerification');
    const btnAnnulerAjout = document.getElementById('btnAnnulerAjout');
    const btnAjouterPiece = document.getElementById('btnAjouterPiece');
    const formMiseAJourKilometrage = document.getElementById('formMiseAJourKilometrage');
    const btnCancelKilometrage = document.getElementById('btnCancelKilometrage');

    // Gestionnaire pour le bouton Nouvelle Vidange
    if (btnNouvelleVidange) {
        btnNouvelleVidange.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Nouvelle Vidange button clicked');
            verificationSection.style.display = 'block';
            ajoutVidangeSection.style.display = 'none';
        });
    }

    // Gestionnaire pour le bouton Annuler de la vérification
    if (btnAnnulerVerification) {
        btnAnnulerVerification.addEventListener('click', function() {
            verificationSection.style.display = 'none';
            document.getElementById('verificationForm').reset();
            document.getElementById('vehiculeInfoDisplay').style.display = 'none';
        });
    }

    // Gestionnaire pour le bouton Confirmer Véhicule
    if (btnConfirmerVehicule) {
        btnConfirmerVehicule.addEventListener('click', function() {
            const matricule = document.getElementById('matricule').value;
            fetch(`/verifier_vehicule?matricule=${encodeURIComponent(matricule)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.vehicule) {
                        document.getElementById('vehicule_id_ajout').value = data.vehicule.id;
                        document.getElementById('ajoutVidangeMatricule').textContent = data.vehicule.matricule;
                        verificationSection.style.display = 'none';
                        ajoutVidangeSection.style.display = 'block';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Une erreur est survenue');
                });
        });
    }

    // Gestionnaire pour le bouton Annuler de l'ajout
    if (btnAnnulerAjout) {
        btnAnnulerAjout.addEventListener('click', function() {
            ajoutVidangeSection.style.display = 'none';
            document.getElementById('ajoutVidangeForm').reset();
        });
    }

    // Gestionnaire pour l'ajout de pièces
    if (btnAjouterPiece) {
        btnAjouterPiece.addEventListener('click', function() {
            const autrePiece = document.getElementById('autrePiece').value.trim();
            if (autrePiece) {
                const piecesContainer = document.getElementById('piecesContainer');
                const newPiece = document.createElement('div');
                newPiece.className = 'form-check';
                newPiece.innerHTML = `
                    <input class="form-check-input" type="checkbox" name="pieces_remplacees[]" value="${autrePiece}" id="piece${piecesContainer.children.length + 1}" checked>
                    <label class="form-check-label" for="piece${piecesContainer.children.length + 1}">${autrePiece}</label>
                `;
                piecesContainer.appendChild(newPiece);
                document.getElementById('autrePiece').value = '';
            }
        });
    }

    // Gestionnaire pour le formulaire de mise à jour du kilométrage
    if (formMiseAJourKilometrage) {
        formMiseAJourKilometrage.addEventListener('submit', function(e) {
            e.preventDefault();
            const vehiculeId = document.getElementById('vehicule_id_update').value;
            const nouveauKilometrage = document.getElementById('nouveau_kilometrage').value;

            fetch('/mise_a_jour_kilometrage', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    vehicule_id: vehiculeId,
                    kilometrage: nouveauKilometrage
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert(data.message || 'Erreur lors de la mise à jour du kilométrage');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Une erreur est survenue');
            });
        });
    }

    // Gestionnaire pour le bouton Annuler de la mise à jour du kilométrage
    if (btnCancelKilometrage) {
        btnCancelKilometrage.addEventListener('click', function() {
            document.getElementById('updateKilometrageSection').style.display = 'none';
            document.getElementById('formMiseAJourKilometrage').reset();
        });
    }

    // Gestionnaire pour les boutons de mise à jour et suppression
    if (vidangesTableBody) {
        vidangesTableBody.addEventListener('click', function(e) {
            const target = e.target.closest('button');
            
            if (target) {
                if (target.classList.contains('btn-update-km')) {
                    const vehiculeId = target.getAttribute('data-vehicule-id');
                    const row = target.closest('tr');
                    const currentKm = row.cells[3].textContent.trim();
                    mettreAJourKilometrage(vehiculeId, currentKm);
                } else if (target.classList.contains('btn-delete-entretien')) {
                    const entretienId = target.getAttribute('data-entretien-id');
                    supprimerEntretien(entretienId);
                }
            }
        });
    }
});

// Fonction pour mettre à jour le kilométrage
function mettreAJourKilometrage(vehiculeId, currentKm) {
    console.log('Updating kilometrage for vehicle:', vehiculeId, 'Current km:', currentKm);
    const kmInput = document.getElementById('nouveau_kilometrage');
    const vehiculeIdInput = document.getElementById('vehicule_id_update');
    
    if (kmInput && vehiculeIdInput) {
        vehiculeIdInput.value = vehiculeId;
        kmInput.value = currentKm;
        document.getElementById('updateKilometrageSection').style.display = 'block';
    }
}

// Fonction pour supprimer un entretien
function supprimerEntretien(entretienId) {
    console.log('Deleting entretien:', entretienId);
    if (confirm('Êtes-vous sûr de vouloir supprimer cette vidange ?')) {
        fetch(`/entretiens/supprimer/${entretienId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                throw new Error(data.message || 'Erreur lors de la suppression');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Une erreur est survenue lors de la suppression');
        });
    }
}
</script>
{% endblock %} 
